# Subscription Scenarios Documentation

This document outlines all possible subscription scenarios in the Dukancard application, including state transitions, webhook handling, and payment method considerations.

## 📋 Table of Contents

1. [Subscription Statuses](#subscription-statuses)
2. [Trial Scenarios](#trial-scenarios)
3. [Authenticated Scenarios](#authenticated-scenarios)
4. [Active Subscription Scenarios](#active-subscription-scenarios)
5. [Halted Subscription Scenarios](#halted-subscription-scenarios)
6. [Idempotency Scenarios](#idempotency-scenarios)
7. [Payment Method Considerations](#payment-method-considerations)
8. [Pause/Resume Logic](#pauseresume-logic)

## 🔄 Subscription Statuses

### Core Statuses
- **`trial`** - User is in trial period, can select plans but hasn't paid
- **`authenticated`** - User has selected a plan but payment is pending
- **`active`** - User has an active paid subscription
- **`halted`** - Subscription paused due to payment failure
- **`pending`** - Payment authorization pending or under review
- **`cancelled`** - Subscription has been cancelled
- **`expired`** - Subscription has expired
- **`completed`** - Subscription billing cycle completed

### Business Profile States
- **`has_active_subscription: true`** - Only for `active` status on paid plans
- **`has_active_subscription: false`** - For all other statuses and free plan

## 🧪 Trial Scenarios

### T1: Trial → Authenticated (Plan Selection)
**Description**: User selects a plan during trial period
- **Initial State**: `trial/growth/false`
- **Webhook**: `subscription.authenticated`
- **Final State**: `authenticated/growth/false`
- **Business Logic**: Plan selected but not yet paid

### T2: Trial → Active (Direct Payment)
**Description**: User pays immediately during trial (rare case)
- **Initial State**: `trial/growth/false`
- **Webhook**: `subscription.activated`
- **Final State**: `active/growth/true`
- **Business Logic**: Direct trial-to-paid conversion

### T3: Trial → Expired → Free
**Description**: Trial period expires without payment
- **Initial State**: `trial/growth/false`
- **Webhook**: `subscription.expired`
- **Final State**: `active/free/false`
- **Business Logic**: Automatic downgrade to free plan

## 🔐 Authenticated Scenarios

### A1: Authenticated → Active (Payment Success)
**Description**: User completes payment after plan selection
- **Initial State**: `authenticated/growth/false`
- **Webhook**: `subscription.activated`
- **Final State**: `active/growth/true`
- **Business Logic**: Normal payment flow completion

### A2: Authenticated → Cancelled → Trial (Plan A Cancellation)
**Description**: User cancels before payment
- **Initial State**: `authenticated/growth/false`
- **Webhook**: `subscription.cancelled`
- **Final State**: `trial/growth/false`
- **Business Logic**: Revert to trial, preserve selected plan

### A3: Authenticated → Charged → Active
**Description**: Payment is charged for authenticated subscription
- **Initial State**: `authenticated/growth/false`
- **Webhook**: `subscription.charged`
- **Final State**: `active/growth/true`
- **Business Logic**: Payment processed, subscription activated

## 💳 Active Subscription Scenarios

### AS1: Active → Charged (Renewal)
**Description**: Recurring payment for active subscription
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.charged`
- **Final State**: `active/growth/true`
- **Business Logic**: Subscription renewed, dates updated

### AS2: Active → Cancelled → Free
**Description**: User cancels active subscription
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.cancelled`
- **Final State**: `active/free/false`
- **Business Logic**: Downgrade to free plan

### AS3: Active → Halted (Payment Failed)
**Description**: Subscription paused due to payment failure
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.halted`
- **Final State**: `halted/free/false`
- **Business Logic**: Store original plan, downgrade to free temporarily

### AS4: Active → Expired → Free
**Description**: Subscription expires and downgrades to free
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.expired`
- **Final State**: `active/free/false`
- **Business Logic**: Automatic downgrade to free plan

### AS5: Active → Completed → Free
**Description**: Subscription completes all billing cycles
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.completed`
- **Final State**: `active/free/false`
- **Business Logic**: Natural end of subscription term

## ⏸️ Halted Subscription Scenarios

### H1: Halted → Reactivated
**Description**: Halted subscription is reactivated after payment
- **Initial State**: `halted/free/false` (original_plan_id: growth)
- **Webhook**: `subscription.activated`
- **Final State**: `active/growth/true`
- **Business Logic**: Restore from original_plan_id

### H2: Halted → Cancelled → Free
**Description**: User cancels halted subscription
- **Initial State**: `halted/free/false`
- **Webhook**: `subscription.cancelled`
- **Final State**: `active/free/false`
- **Business Logic**: Permanent downgrade to free

### H3: Halted → Expired → Free
**Description**: Halted subscription expires
- **Initial State**: `halted/free/false`
- **Webhook**: `subscription.expired`
- **Final State**: `active/free/false`
- **Business Logic**: Permanent downgrade to free

## � Idempotency Scenarios

### I1: Duplicate Subscription Activated
**Description**: Duplicate activation webhook should be ignored
- **Initial State**: `authenticated/growth/false`
- **Webhook**: `subscription.activated` (duplicate with same event_id)
- **Final State**: `active/growth/true` (unchanged after first processing)
- **Business Logic**: processed_webhook_events table prevents duplicate processing
- **Validation**: Event recorded only once, state unchanged on duplicate

### I2: Duplicate Subscription Charged
**Description**: Duplicate charge webhook should be ignored
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.charged` (duplicate with same event_id)
- **Final State**: `active/growth/true` (unchanged)
- **Business Logic**: Renewal payment idempotency protection
- **Validation**: No double charging, subscription dates unchanged

### I3: Duplicate Subscription Cancelled
**Description**: Duplicate cancellation webhook should be ignored
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.cancelled` (duplicate with same event_id)
- **Final State**: `active/free/false` (unchanged after first processing)
- **Business Logic**: Cancellation idempotency protection
- **Validation**: No double downgrade, state consistent

### I4: Duplicate Subscription Halted
**Description**: Duplicate halt webhook should be ignored
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.halted` (duplicate with same event_id)
- **Final State**: `halted/free/false` (unchanged after first processing)
- **Business Logic**: Payment failure idempotency protection
- **Validation**: Original plan data preserved, no duplicate halt processing

### I5: Rapid Duplicate Processing
**Description**: Multiple rapid duplicates should be handled atomically
- **Initial State**: `authenticated/growth/false`
- **Webhook**: `subscription.activated` (3 rapid duplicates with same event_id)
- **Final State**: `active/growth/true` (processed once only)
- **Business Logic**: Race condition protection in idempotency mechanism
- **Validation**: Only one processed_webhook_events record, atomic state change

### I6: Same Event ID Different Payload
**Description**: Same event ID with different payload should be rejected
- **Initial State**: `authenticated/growth/false`
- **Webhook**: `subscription.activated` (same event_id, different subscription data)
- **Final State**: `active/growth/true` (first payload processed, second rejected)
- **Business Logic**: Event ID uniqueness validation
- **Validation**: Payload consistency enforced, duplicate event_id rejected

### I7: Out-of-Order Webhook with Duplicate
**Description**: Out-of-order webhook followed by duplicate should both be rejected
- **Initial State**: `active/growth/true`
- **Webhook**: `subscription.charged` (old timestamp + duplicate)
- **Final State**: `active/growth/true` (both webhooks rejected)
- **Business Logic**: Sequence validation + idempotency combined
- **Validation**: Both out-of-order and duplicate processing prevented

## �💰 Payment Method Considerations

### Card Payments (Updatable)
- **Behavior**: Subscription can be updated in-place
- **Plan Changes**: Direct update without cancellation
- **Cycle Changes**: Direct update without cancellation
- **User Experience**: Seamless plan switching

### UPI/Emandate Payments (Non-Updatable)
- **Behavior**: Create new subscription, cancel old one
- **Plan Changes**: Create-new-cancel-old pattern
- **Cycle Changes**: Create-new-cancel-old pattern
- **User Experience**: Requires user confirmation dialog

### Payment Method Detection Flow
1. **User clicks "View Plan" → "Subscribe Now"**
2. **Detect current payment method**:
   ```typescript
   // Fetch from Razorpay API, not Supabase
   const subscription = await razorpay.subscriptions.fetch(subscriptionId);
   const paymentMethod = subscription.payment_method;
   ```
3. **Show appropriate dialog**:
   - **Card**: "Update your subscription plan"
   - **UPI/Emandate**: "Create new subscription (old will be cancelled)"

### Implementation Logic
```typescript
if (paymentMethod === 'card') {
  // Direct update flow
  await updateSubscription(subscriptionId, newPlan);
} else if (['upi', 'emandate'].includes(paymentMethod)) {
  // Create-new-cancel-old flow
  const newSubscription = await createSubscription(newPlan);
  // Old subscription cancelled when new becomes active
}
```

## 🔄 Pause/Resume Logic

### Pause Subscription
1. **Store current state**:
   - `original_plan_id` = current plan_id
   - `original_plan_cycle` = current plan_cycle
   - `subscription_paused_at` = current timestamp
2. **Downgrade temporarily**:
   - `plan_id` = 'free'
   - `plan_cycle` = 'monthly'
   - `subscription_status` = 'halted'
   - `has_active_subscription` = false

### Resume Subscription
1. **Restore original state**:
   - `plan_id` = original_plan_id
   - `plan_cycle` = original_plan_cycle
   - `subscription_status` = 'active'
   - `has_active_subscription` = true
2. **Clear pause data**:
   - `original_plan_id` = null
   - `original_plan_cycle` = null
   - `subscription_paused_at` = null

## 🎯 Key Business Rules

### Free Plan Rules
- `plan_id: 'free'`
- `has_active_subscription: false`
- `subscription_status: 'active'`
- Terminal state for downgrades

### Trial Rules
- `has_active_subscription: false` (not paying yet)
- Can select plans (authenticated status)
- Trial expiry → free plan

### Authenticated Rules
- `has_active_subscription: false` (not yet paid)
- Can activate (payment) or cancel (revert to trial)
- Plan A cancellation preserves selected plan

### Active Rules
- `has_active_subscription: true` (paying customer)
- Can be charged (renewals), cancelled, halted, expired
- Cancellation → free plan downgrade

### Atomic Operations
- All state changes use `update_subscription_atomic` RPC
- Dual table updates (payment_subscriptions + business_profiles)
- Transaction safety and consistency guaranteed
- Race condition protection via webhook sequence validation

## 🔧 Testing Coverage

All scenarios are covered by the comprehensive webhook testing suite with **real-world rigorous testing**:

### Core Scenarios
- **Trial Scenarios**: 4 tests (100% coverage)
  - Trial → Authenticated (Plan Selection)
  - Trial → Active (Direct Payment)
  - Trial → Expired → Free
  - Trial → Premium Yearly (Direct Activation)

- **Transition Scenarios**: 12 tests (authentication flows, halted recovery, complex state changes)
  - Authenticated → Active (Card/UPI/E-Mandate)
  - Authenticated → Cancelled → Trial
  - Authenticated → Charged → Active
  - Authenticated → Pending (Invalid Transition Test)
  - Halted → Reactivated
  - Halted → Cancelled → Free
  - Halted → Expired → Free
  - Pending → Active (Review Approved)

- **Paid Scenarios**: 15 tests (renewals, cancellations, expirations, plan changes)
  - Active → Charged (Renewal)
  - Active → Cancelled → Free
  - Active → Halted (Payment Failed)
  - Active → Expired → Free
  - Active → Completed → Free
  - Active → Updated (Plan/Cycle Change)
  - UPI/E-Mandate Create-New-Cancel-Old patterns
  - Multiple subscription coordination

- **Idempotency Scenarios**: 12 tests (duplicate webhook protection)
  - Duplicate Subscription Activated
  - Duplicate Subscription Charged
  - Duplicate Subscription Cancelled
  - Duplicate Subscription Halted
  - Duplicate Subscription Expired
  - Duplicate Subscription Completed
  - Duplicate Subscription Updated
  - Duplicate Subscription Authenticated
  - Duplicate Subscription Pending
  - Rapid Duplicate Processing
  - Same Event ID Different Payload
  - Out-of-Order Webhook with Duplicate

### Real-World Edge Cases & Rigorous Testing
- **Race Condition Tests**: 2 scenarios
  - Authenticated vs Cancelled race conditions
  - Active vs Halted simultaneous processing

- **Payment Method Specific Tests**: 8 scenarios
  - **Card Payments**: Direct update patterns
    - Plan upgrades (Monthly → Yearly)
    - Plan downgrades (Premium → Basic)
  - **UPI Payments**: Create-new-cancel-old patterns
    - Plan + cycle changes
    - Multiple subscription coordination
  - **E-Mandate Payments**: Create-new-cancel-old patterns
    - Cycle-only changes
    - Old subscription cleanup

- **Complex State Transitions**: 6 scenarios
  - Trial → Premium Yearly (Direct)
  - Authenticated → Pending → Active (Payment Review)
  - Pause/Resume with original plan storage
  - Multiple subscription scenarios

- **Invalid Transition Tests**: 2 scenarios
  - Free → Authenticated (Should Reject)
  - Completed → Active (Should Reject)

### Advanced Testing Features
- **Idempotency Validation**: All webhooks tested for duplicate processing
- **Out-of-Order Webhook Handling**: Sequence validation prevents late events
- **Atomic Transaction Testing**: Database consistency across dual-table updates
- **Payment Method Detection**: Real Razorpay API integration testing
- **Create-New-Cancel-Old Coordination**: UPI/E-Mandate subscription replacement flows
- **processed_webhook_events Table**: Event uniqueness and duplicate prevention
- **Rapid Duplicate Handling**: Race condition protection during concurrent processing
- **Event ID Validation**: Payload consistency enforcement for same event IDs

Each test validates:
- ✅ Webhook processing success
- ✅ Database state consistency across both tables
- ✅ Business rule compliance
- ✅ Atomic transaction completion
- ✅ Payment method specific behavior
- ✅ Race condition handling
- ✅ Edge case robustness
- ✅ Real-world scenario simulation
- ✅ Idempotency protection (when enabled with --idempotency flag)
- ✅ processed_webhook_events table integrity
- ✅ Duplicate webhook rejection
- ✅ Event sequence validation
