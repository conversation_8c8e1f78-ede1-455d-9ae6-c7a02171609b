/**
 * SCENARIO TEST RUNNER
 * 
 * Runs individual subscription scenarios and validates webhook handling
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

import type { SubscriptionScenario } from '../scenarios/subscriptionScenarios';
import { generateWebhookPayload } from '../events/webhookEvents';

export interface ScenarioTestResult {
  scenario: SubscriptionScenario;
  success: boolean;
  message: string;
  duration: number;
  details: {
    initialState: Record<string, unknown>;
    finalState: Record<string, unknown>;
    webhookResult: { success: boolean; message: string };
    idempotencyResult?: { success: boolean; message: string; details?: any } | null;
    stateMatches: boolean;
  };
}

export class ScenarioTestRunner {
  private businessId: string;
  private adminClient: any;

  constructor(businessId: string) {
    this.businessId = businessId;
  }

  /**
   * Initialize the test runner
   */
  async initialize(): Promise<void> {
    const { createAdminClient } = await import('../../../utils/supabase/admin');
    this.adminClient = createAdminClient();
  }

  /**
   * Run a single scenario test with optional idempotency testing
   */
  async runScenario(scenario: SubscriptionScenario, testIdempotency: boolean = false): Promise<ScenarioTestResult> {
    const startTime = Date.now();

    try {
      console.log(`\n🧪 Testing: ${scenario.name}`);
      console.log(`   Description: ${scenario.description}`);

      // 1. Setup initial state
      await this.setupInitialState(scenario);

      // 2. Get initial state for comparison
      const initialState = await this.getCurrentState();
      console.log(`   Initial: ${initialState.subscription_status}/${initialState.plan_id}/${initialState.has_active_subscription}`);

      // 3. Generate and send webhook
      const webhookResult = await this.sendWebhook(scenario);
      console.log(`   Webhook: ${webhookResult.success ? '✅' : '❌'} ${webhookResult.message}`);

      // 4. Test idempotency if requested or if this is an idempotency scenario
      let idempotencyResult = null;
      const shouldTestIdempotency = testIdempotency || scenario.isIdempotencyTest;
      if (shouldTestIdempotency && webhookResult.success) {
        idempotencyResult = await this.testIdempotency(scenario);
        console.log(`   Idempotency: ${idempotencyResult.success ? '✅' : '❌'} ${idempotencyResult.message}`);
      }

      // 5. Get final state
      const finalState = await this.getCurrentState();
      console.log(`   Final: ${finalState.subscription_status}/${finalState.plan_id}/${finalState.has_active_subscription}`);

      // 6. Validate state transition
      const stateMatches = this.validateStateTransition(scenario, finalState);

      const success = scenario.shouldSucceed ?
        (webhookResult.success && stateMatches && (!shouldTestIdempotency || (idempotencyResult?.success === true))) :
        (webhookResult.success && webhookResult.message.includes('STATE REJECTION') && stateMatches); // For failed scenarios, expect STATE REJECTION and unchanged state

      return {
        scenario,
        success,
        message: success ?
          `Scenario passed - state transition correct${shouldTestIdempotency ? ' and idempotency working' : ''}` :
          `Scenario failed - expected state not reached${idempotencyResult && !idempotencyResult.success ? ' or idempotency failed' : ''}`,
        duration: (Date.now() - startTime) / 1000,
        details: {
          initialState,
          finalState,
          webhookResult,
          idempotencyResult,
          stateMatches
        }
      };

    } catch (error) {
      return {
        scenario,
        success: false,
        message: `Scenario failed with error: ${error instanceof Error ? error.message : String(error)}`,
        duration: (Date.now() - startTime) / 1000,
        details: {
          initialState: {},
          finalState: {},
          webhookResult: { success: false, message: 'Error occurred' },
          idempotencyResult: null,
          stateMatches: false
        }
      };
    }
  }

  /**
   * Setup initial state for the scenario
   */
  private async setupInitialState(scenario: SubscriptionScenario): Promise<void> {
    const subscriptionId = `sub_test_${scenario.id}_${Date.now()}`;

    // For create-new-cancel-old scenarios, we need to setup an old subscription first
    if (scenario.isCreateNewCancelOld) {
      await this.setupCreateNewCancelOldScenario(scenario, subscriptionId);
    } else {
      // Standard scenario setup
      await this.setupStandardScenario(scenario, subscriptionId);
    }
  }

  /**
   * Setup standard scenario (direct update pattern)
   */
  private async setupStandardScenario(scenario: SubscriptionScenario, subscriptionId: string): Promise<void> {
    // Handle special scenarios that need additional data
    const additionalData: Record<string, unknown> = {};

    // For halted-to-active scenarios, set up original plan data
    if (scenario.id === 'halted_to_active_restore_original_plan') {
      additionalData.original_plan_id = 'premium';
      additionalData.original_plan_cycle = 'yearly';
      additionalData.subscription_paused_at = new Date().toISOString();
    }

    // For delayed webhook processing scenario, set up original plan data
    if (scenario.id === 'delayed_webhook_processing') {
      additionalData.original_plan_id = 'premium';
      additionalData.original_plan_cycle = 'yearly';
      additionalData.subscription_paused_at = new Date().toISOString();
    }

    // For active-to-halted scenarios with original plan storage
    if (scenario.id === 'active_to_halted_with_original_plan_storage') {
      // This will be set by the webhook handler, but we ensure clean state
      additionalData.original_plan_id = null;
      additionalData.original_plan_cycle = null;
      additionalData.subscription_paused_at = null;
    }

    // Update payment_subscriptions table - clear cancelled_at for fresh scenarios
    await this.adminClient
      .from('payment_subscriptions')
      .update({
        razorpay_subscription_id: subscriptionId,
        plan_id: scenario.initialState.plan_id,
        plan_cycle: scenario.initialState.plan_cycle,
        subscription_status: scenario.initialState.subscription_status,
        cancelled_at: null, // Clear any previous cancellation timestamp
        last_payment_method: scenario.paymentMethod || 'card',
        updated_at: new Date().toISOString(),
        ...additionalData
      })
      .eq('business_profile_id', this.businessId);

    // Update business_profiles table
    await this.adminClient
      .from('business_profiles')
      .update({
        has_active_subscription: scenario.initialState.has_active_subscription,
        updated_at: new Date().toISOString()
      })
      .eq('id', this.businessId);
  }

  /**
   * Setup create-new-cancel-old scenario (UPI/emandate pattern)
   * For these scenarios, we simulate the real-world flow where:
   * 1. User has an existing active subscription (old)
   * 2. User creates a new subscription for plan change (new)
   * 3. New subscription gets activated, old gets cancelled
   */
  private async setupCreateNewCancelOldScenario(scenario: SubscriptionScenario, _newSubscriptionId: string): Promise<void> {
    const oldSubscriptionId = `sub_old_${scenario.id}_${Date.now()}`;

    if (scenario.webhookEvent === 'subscription.activated') {
      // Scenario: New subscription activation (which should cancel old one)
      // Setup: Old active subscription exists, new subscription will be activated

      // Setup the old active subscription
      // IMPORTANT: Clear original_plan_id fields to ensure clean state
      await this.adminClient
        .from('payment_subscriptions')
        .update({
          razorpay_subscription_id: oldSubscriptionId,
          plan_id: scenario.initialState.plan_id,
          plan_cycle: scenario.initialState.plan_cycle,
          subscription_status: scenario.initialState.subscription_status,
          cancelled_at: null,
          last_payment_method: scenario.paymentMethod || 'upi',
          original_plan_id: null, // Clear to ensure clean state
          original_plan_cycle: null, // Clear to ensure clean state
          subscription_paused_at: null, // Clear to ensure clean state
          updated_at: new Date().toISOString()
        })
        .eq('business_profile_id', this.businessId);

      // Store the old subscription ID for webhook processing
      scenario.oldSubscriptionId = oldSubscriptionId;

    } else if (scenario.webhookEvent === 'subscription.cancelled') {
      // Scenario: Old subscription cancellation (after new one is active)
      // Setup: Old subscription exists and will be cancelled

      await this.adminClient
        .from('payment_subscriptions')
        .update({
          razorpay_subscription_id: oldSubscriptionId,
          plan_id: scenario.initialState.plan_id,
          plan_cycle: scenario.initialState.plan_cycle,
          subscription_status: scenario.initialState.subscription_status,
          cancelled_at: null,
          last_payment_method: scenario.paymentMethod || 'upi',
          original_plan_id: null, // Clear to ensure clean state
          original_plan_cycle: null, // Clear to ensure clean state
          subscription_paused_at: null, // Clear to ensure clean state
          updated_at: new Date().toISOString()
        })
        .eq('business_profile_id', this.businessId);

      // Store the old subscription ID for webhook processing
      scenario.oldSubscriptionId = oldSubscriptionId;
    }

    // Update business_profiles table
    await this.adminClient
      .from('business_profiles')
      .update({
        has_active_subscription: scenario.initialState.has_active_subscription,
        updated_at: new Date().toISOString()
      })
      .eq('id', this.businessId);
  }

  /**
   * Send webhook for the scenario
   */
  private async sendWebhook(scenario: SubscriptionScenario): Promise<{ success: boolean; message: string }> {
    if (scenario.isCreateNewCancelOld) {
      return await this.sendCreateNewCancelOldWebhook(scenario);
    } else {
      return await this.sendStandardWebhook(scenario);
    }
  }

  /**
   * Send webhook for standard scenarios (direct update)
   */
  private async sendStandardWebhook(scenario: SubscriptionScenario): Promise<{ success: boolean; message: string }> {
    // Get the existing subscription ID from the database
    const { data: existingSubscription } = await this.adminClient
      .from('payment_subscriptions')
      .select('razorpay_subscription_id')
      .eq('business_profile_id', this.businessId)
      .single();

    const _subscriptionId = existingSubscription?.razorpay_subscription_id || `sub_test_${scenario.id}_${Date.now()}`;

    // Update the subscription ID in the database to match what we'll send in the webhook
    const _newSubscriptionId = `sub_test_${scenario.id}_${Date.now()}`;
    await this.adminClient
      .from('payment_subscriptions')
      .update({
        razorpay_subscription_id: _newSubscriptionId,
        updated_at: new Date().toISOString()
      })
      .eq('business_profile_id', this.businessId);

    // Get the appropriate webhook handler
    const handler = await this.getWebhookHandler(scenario.webhookEvent);
    if (!handler) {
      return { success: false, message: `No handler found for event: ${scenario.webhookEvent}` };
    }

    // Generate webhook payload
    const payload = generateWebhookPayload(
      scenario.webhookEvent,
      _newSubscriptionId,
      this.businessId,
      {
        plan_id: scenario.initialState.plan_id,
        plan_cycle: scenario.initialState.plan_cycle,
        payment_method: scenario.paymentMethod || 'card'
      }
    );

    // Send webhook
    const eventId = `test_${scenario.id}_${Date.now()}`;
    return await (handler as any)(payload, this.adminClient, eventId);
  }

  /**
   * Send webhook for create-new-cancel-old scenarios (UPI/emandate)
   */
  private async sendCreateNewCancelOldWebhook(scenario: SubscriptionScenario): Promise<{ success: boolean; message: string }> {
    if (scenario.webhookEvent === 'subscription.activated') {
      // For activation, we need to simulate a new subscription being activated
      // In real world, this subscription would have been created via API call
      // and then activated via webhook

      const newSubscriptionId = `sub_new_${scenario.id}_${Date.now()}`;

      // For create-new-cancel-old, we simulate by updating the existing subscription
      // to represent the new subscription that will be activated
      // IMPORTANT: Clear original_plan_id fields to prevent resume flow detection
      await this.adminClient
        .from('payment_subscriptions')
        .update({
          razorpay_subscription_id: newSubscriptionId,
          plan_id: scenario.expectedFinalState.plan_id,
          plan_cycle: scenario.expectedFinalState.plan_cycle,
          subscription_status: 'authenticated', // Will be activated by webhook
          last_payment_method: scenario.paymentMethod || 'upi',
          original_plan_id: null, // Clear to prevent resume flow detection
          original_plan_cycle: null, // Clear to prevent resume flow detection
          subscription_paused_at: null, // Clear to prevent resume flow detection
          updated_at: new Date().toISOString()
        })
        .eq('business_profile_id', this.businessId);

      // Get the appropriate webhook handler
      const handler = await this.getWebhookHandler(scenario.webhookEvent);
      if (!handler) {
        return { success: false, message: `No handler found for event: ${scenario.webhookEvent}` };
      }

      // Generate webhook payload with old_subscription_id in notes
      const payload = generateWebhookPayload(
        scenario.webhookEvent,
        newSubscriptionId,
        this.businessId,
        {
          plan_id: scenario.expectedFinalState.plan_id,
          plan_cycle: scenario.expectedFinalState.plan_cycle,
          payment_method: scenario.paymentMethod || 'upi',
          old_subscription_id: scenario.oldSubscriptionId // Include old subscription for cancellation
        }
      );

      // Send webhook
      const eventId = `test_${scenario.id}_${Date.now()}`;
      return await (handler as any)(payload, this.adminClient, eventId);

    } else if (scenario.webhookEvent === 'subscription.cancelled') {
      // For cancellation, send webhook for the old subscription
      const oldSubscriptionId = scenario.oldSubscriptionId || `sub_old_${scenario.id}_${Date.now()}`;

      // Get the appropriate webhook handler
      const handler = await this.getWebhookHandler(scenario.webhookEvent);
      if (!handler) {
        return { success: false, message: `No handler found for event: ${scenario.webhookEvent}` };
      }

      // Generate webhook payload
      const payload = generateWebhookPayload(
        scenario.webhookEvent,
        oldSubscriptionId,
        this.businessId,
        {
          plan_id: scenario.initialState.plan_id,
          plan_cycle: scenario.initialState.plan_cycle,
          payment_method: scenario.paymentMethod || 'upi'
        }
      );

      // Send webhook
      const eventId = `test_${scenario.id}_${Date.now()}`;
      return await (handler as any)(payload, this.adminClient, eventId);
    }

    return { success: false, message: `Unsupported webhook event for create-new-cancel-old: ${scenario.webhookEvent}` };
  }

  /**
   * Get webhook handler for event type
   */
  private async getWebhookHandler(eventType: string): Promise<unknown | null> {
    try {
      switch (eventType) {
        case 'subscription.authenticated':
          const { handleSubscriptionAuthenticated } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
          return handleSubscriptionAuthenticated;
          
        case 'subscription.activated':
          const { handleSubscriptionActivated } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
          return handleSubscriptionActivated;
          
        case 'subscription.charged':
          const { handleSubscriptionCharged } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
          return handleSubscriptionCharged;
          
        case 'subscription.pending':
          const { handleSubscriptionPending } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionHandlers');
          return handleSubscriptionPending;
          
        case 'subscription.halted':
          const { handleSubscriptionHalted } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionHalted');
          return handleSubscriptionHalted;
          
        case 'subscription.cancelled':
          const { handleSubscriptionCancelled } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCancelled');
          return handleSubscriptionCancelled;
          
        case 'subscription.completed':
          const { handleSubscriptionCompleted } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCompleted');
          return handleSubscriptionCompleted;
          
        case 'subscription.expired':
          const { handleSubscriptionExpired } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionExpired');
          return handleSubscriptionExpired;
          
        case 'subscription.updated':
          const { handleSubscriptionUpdated } = await import('../../../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionUpdated');
          return handleSubscriptionUpdated;
          
        default:
          return null;
      }
    } catch (error) {
      console.error(`Error loading handler for ${eventType}:`, error);
      return null;
    }
  }

  /**
   * Get current subscription state
   */
  private async getCurrentState(): Promise<Record<string, unknown>> {
    const { data: subscription } = await this.adminClient
      .from('payment_subscriptions')
      .select('subscription_status, plan_id, plan_cycle')
      .eq('business_profile_id', this.businessId)
      .single();

    const { data: profile } = await this.adminClient
      .from('business_profiles')
      .select('has_active_subscription')
      .eq('id', this.businessId)
      .single();

    return {
      subscription_status: subscription?.subscription_status,
      plan_id: subscription?.plan_id,
      plan_cycle: subscription?.plan_cycle,
      has_active_subscription: profile?.has_active_subscription
    };
  }

  /**
   * Test idempotency by sending duplicate webhooks
   */
  private async testIdempotency(scenario: SubscriptionScenario): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // For idempotency scenarios, we need to test more comprehensively
      if (scenario.isIdempotencyTest) {
        return await this.testComprehensiveIdempotency(scenario);
      }

      // Standard idempotency test for regular scenarios
      return await this.testBasicIdempotency(scenario);

    } catch (error) {
      return {
        success: false,
        message: `Idempotency test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Test basic idempotency by sending duplicate webhooks
   */
  private async testBasicIdempotency(scenario: SubscriptionScenario): Promise<{ success: boolean; message: string; details?: any }> {
    // Get current state before duplicate webhook
    const stateBeforeDuplicate = await this.getCurrentState();

    // Get current processed events count
    const eventId = `test_${scenario.id}_${Date.now()}_duplicate`;
    const processedEventsBefore = await this.getProcessedEventsCount(eventId);

    // Send the same webhook again (duplicate)
    const duplicateResult = await this.sendWebhookWithEventId(scenario, eventId);

    // Get state after duplicate webhook
    const stateAfterDuplicate = await this.getCurrentState();

    // Get processed events count after
    const processedEventsAfter = await this.getProcessedEventsCount(eventId);

    // Check if state remained unchanged
    const stateUnchanged = JSON.stringify(stateBeforeDuplicate) === JSON.stringify(stateAfterDuplicate);

    // Check if duplicate was handled idempotently
    const wasIdempotent = duplicateResult.success && (
      duplicateResult.message.includes('already processed') ||
      duplicateResult.message.includes('idempotent') ||
      stateUnchanged
    );

      // Check that only one processed event record exists
      const correctEventCount = processedEventsAfter <= processedEventsBefore + 1;

      const success = wasIdempotent && stateUnchanged && correctEventCount;

      return {
        success,
        message: success
          ? 'Duplicate webhook handled idempotently'
          : 'Idempotency failed - duplicate processing detected',
        details: {
          stateBeforeDuplicate,
          stateAfterDuplicate,
          stateUnchanged,
          duplicateResult,
          processedEventsBefore,
          processedEventsAfter,
          correctEventCount
        }
      };
  }

  /**
   * Test comprehensive idempotency for idempotency-specific scenarios
   */
  private async testComprehensiveIdempotency(scenario: SubscriptionScenario): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // Test multiple rapid duplicates
      const eventId = `test_${scenario.id}_${Date.now()}_comprehensive`;
      const results = [];

      // Send 3 rapid duplicate webhooks
      for (let i = 0; i < 3; i++) {
        const result = await this.sendWebhookWithEventId(scenario, eventId);
        results.push(result);
        // Small delay to simulate rapid processing
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Check that only the first one was processed
      const processedCount = await this.getProcessedEventsCount(eventId);
      const firstSucceeded = results[0].success;
      const subsequentIgnored = results.slice(1).every(r =>
        r.success && (r.message.includes('already processed') || r.message.includes('idempotent'))
      );

      const success = firstSucceeded && subsequentIgnored && processedCount === 1;

      return {
        success,
        message: success
          ? 'Comprehensive idempotency test passed'
          : 'Comprehensive idempotency test failed',
        details: {
          results,
          processedCount,
          firstSucceeded,
          subsequentIgnored
        }
      };

    } catch (error) {
      return {
        success: false,
        message: `Comprehensive idempotency test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Send webhook with specific event ID for idempotency testing
   */
  private async sendWebhookWithEventId(scenario: SubscriptionScenario, eventId: string): Promise<{ success: boolean; message: string }> {
    // Get current subscription ID
    const { data: existingSubscription } = await this.adminClient
      .from('payment_subscriptions')
      .select('razorpay_subscription_id')
      .eq('business_profile_id', this.businessId)
      .single();

    const subscriptionId = existingSubscription?.razorpay_subscription_id || `sub_test_${scenario.id}_${Date.now()}`;

    // Get the appropriate webhook handler
    const handler = await this.getWebhookHandler(scenario.webhookEvent);
    if (!handler) {
      return { success: false, message: `No handler found for event: ${scenario.webhookEvent}` };
    }

    // Generate webhook payload
    const payload = generateWebhookPayload(
      scenario.webhookEvent,
      subscriptionId,
      this.businessId,
      {
        plan_id: scenario.initialState.plan_id,
        plan_cycle: scenario.initialState.plan_cycle,
        payment_method: scenario.paymentMethod || 'card'
      }
    );

    // Send webhook with specific event ID
    return await (handler as any)(payload, this.adminClient, eventId);
  }

  /**
   * Get count of processed webhook events for a specific event ID
   */
  private async getProcessedEventsCount(eventId: string): Promise<number> {
    try {
      const { data, error } = await this.adminClient
        .from('processed_webhook_events')
        .select('event_id')
        .eq('event_id', eventId);

      if (error) {
        console.error('Error fetching processed events:', error);
        return 0;
      }

      return data?.length || 0;
    } catch (error) {
      console.error('Error in getProcessedEventsCount:', error);
      return 0;
    }
  }

  /**
   * Validate state transition matches expected outcome
   */
  private validateStateTransition(scenario: SubscriptionScenario, finalState: Record<string, unknown>): boolean {
    const expected = scenario.expectedFinalState;

    return (
      finalState.subscription_status === expected.subscription_status &&
      finalState.plan_id === expected.plan_id &&
      finalState.plan_cycle === expected.plan_cycle &&
      finalState.has_active_subscription === expected.has_active_subscription
    );
  }
}
