#!/usr/bin/env tsx

/**
 * TEST IDEMPOTENCY SCENARIOS
 * 
 * Quick test to verify idempotency scenarios are properly loaded and configured
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

import { 
  ALL_SUBSCRIPTION_SCENARIOS, 
  IDEMPOTENCY_SCENARIOS,
  getScenariosByCategory 
} from './scenarios/subscriptionScenarios';

async function testIdempotencyScenarios() {
  console.log('🔄 Testing Idempotency Scenarios Configuration');
  console.log('='.repeat(50));

  // Test 1: Check if idempotency scenarios are loaded
  console.log(`\n📊 Total scenarios: ${ALL_SUBSCRIPTION_SCENARIOS.length}`);
  console.log(`🔄 Idempotency scenarios: ${IDEMPOTENCY_SCENARIOS.length}`);

  // Test 2: Check if idempotency scenarios are properly categorized
  const idempotencyByCategory = getScenariosByCategory('idempotency');
  console.log(`📂 Idempotency scenarios by category: ${idempotencyByCategory.length}`);

  // Test 3: List all idempotency scenarios
  console.log('\n🔄 Idempotency Scenarios:');
  console.log('-'.repeat(50));
  
  idempotencyByCategory.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.name}`);
    console.log(`   ID: ${scenario.id}`);
    console.log(`   Event: ${scenario.webhookEvent}`);
    console.log(`   Should Succeed: ${scenario.shouldSucceed}`);
    console.log(`   Is Idempotency Test: ${scenario.isIdempotencyTest}`);
    console.log('');
  });

  // Test 4: Verify all idempotency scenarios have the correct flags
  const allHaveIdempotencyFlag = idempotencyByCategory.every(s => s.isIdempotencyTest === true);
  const allHaveCorrectCategory = idempotencyByCategory.every(s => s.category === 'idempotency');

  console.log('✅ Validation Results:');
  console.log(`   All have idempotency flag: ${allHaveIdempotencyFlag ? '✅' : '❌'}`);
  console.log(`   All have correct category: ${allHaveCorrectCategory ? '✅' : '❌'}`);

  // Test 5: Check webhook event coverage
  const webhookEvents = new Set(idempotencyByCategory.map(s => s.webhookEvent));
  console.log(`\n🎯 Webhook Events Covered (${webhookEvents.size}):`);
  Array.from(webhookEvents).sort().forEach(event => {
    const count = idempotencyByCategory.filter(s => s.webhookEvent === event).length;
    console.log(`   ${event}: ${count} scenario(s)`);
  });

  // Test 6: Summary
  console.log('\n' + '='.repeat(50));
  console.log('📋 SUMMARY:');
  console.log(`✅ Total idempotency scenarios: ${idempotencyByCategory.length}`);
  console.log(`✅ Webhook events covered: ${webhookEvents.size}`);
  console.log(`✅ All scenarios properly configured: ${allHaveIdempotencyFlag && allHaveCorrectCategory}`);
  console.log('='.repeat(50));

  return {
    totalScenarios: idempotencyByCategory.length,
    webhookEventsCovered: webhookEvents.size,
    allProperlyConfigured: allHaveIdempotencyFlag && allHaveCorrectCategory
  };
}

// Run if called directly
if (require.main === module) {
  testIdempotencyScenarios()
    .then(result => {
      if (result.allProperlyConfigured) {
        console.log('\n🎉 All idempotency scenarios are properly configured!');
        process.exit(0);
      } else {
        console.log('\n❌ Some idempotency scenarios have configuration issues.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

export { testIdempotencyScenarios };
