#!/usr/bin/env tsx

/**
 * COMPREHENSIVE WEBHOOK TESTING SUITE
 * 
 * Tests all subscription scenarios and webhook events to ensure
 * complete coverage of the subscription lifecycle.
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

import { 
  ALL_SUBSCRIPTION_SCENARIOS, 
  getScenariosByCategory,
  type SubscriptionScenario 
} from './scenarios/subscriptionScenarios';
import { ScenarioTestRunner, type ScenarioTestResult } from './runners/scenarioTestRunner';

const BUSINESS_ID = 'd4fe2395-3872-4522-9b67-0d280633f318';

interface TestSuiteResult {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: ScenarioTestResult[];
  duration: number;
  categories: {
    trial: { passed: number; total: number };
    paid: { passed: number; total: number };
    free: { passed: number; total: number };
    transition: { passed: number; total: number };
    idempotency: { passed: number; total: number };
    network: { passed: number; total: number };
    business: { passed: number; total: number };
    volume: { passed: number; total: number };
  };
}

async function runComprehensiveTests(testIdempotency: boolean = false): Promise<TestSuiteResult> {
  const startTime = Date.now();
  console.log('🚀 Comprehensive Webhook Testing Suite');
  console.log('=====================================');
  console.log(`📋 Business ID: ${BUSINESS_ID}`);
  console.log(`📊 Total Scenarios: ${ALL_SUBSCRIPTION_SCENARIOS.length}`);
  if (testIdempotency) {
    console.log('🔄 Idempotency Testing: ENABLED');
  }
  console.log('');

  const runner = new ScenarioTestRunner(BUSINESS_ID);
  await runner.initialize();

  const results: ScenarioTestResult[] = [];

  // Run tests by category for better organization
  const categories = ['trial', 'transition', 'paid', 'free', 'idempotency', 'network', 'business', 'volume'] as const;

  for (const category of categories) {
    const scenarios = getScenariosByCategory(category);
    if (scenarios.length === 0) continue;

    console.log(`\n📂 Testing ${category.toUpperCase()} Scenarios (${scenarios.length} tests)`);
    console.log('─'.repeat(60));

    for (const scenario of scenarios) {
      const result = await runner.runScenario(scenario, testIdempotency);
      results.push(result);

      // Display result
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${scenario.name} (${result.duration.toFixed(2)}s)`);

      if (!result.success) {
        console.log(`     ❌ ${result.message}`);
        if (result.details.webhookResult && !result.details.webhookResult.success) {
          console.log(`     🔧 Webhook Error: ${result.details.webhookResult.message}`);
        }
        if (result.details.idempotencyResult && !result.details.idempotencyResult.success) {
          console.log(`     🔄 Idempotency Error: ${result.details.idempotencyResult.message}`);
        }
      }

      // Small delay between tests to avoid race conditions
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // Calculate results
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  
  // Calculate category results
  const categoryResults = {
    trial: calculateCategoryResults(results, 'trial'),
    paid: calculateCategoryResults(results, 'paid'),
    free: calculateCategoryResults(results, 'free'),
    transition: calculateCategoryResults(results, 'transition'),
    idempotency: calculateCategoryResults(results, 'idempotency'),
    network: calculateCategoryResults(results, 'network'),
    business: calculateCategoryResults(results, 'business'),
    volume: calculateCategoryResults(results, 'volume')
  };

  return {
    totalTests,
    passedTests,
    failedTests,
    results,
    duration: (Date.now() - startTime) / 1000,
    categories: categoryResults
  };
}

function calculateCategoryResults(results: ScenarioTestResult[], category: string) {
  const categoryResults = results.filter(r => r.scenario.category === category);
  return {
    passed: categoryResults.filter(r => r.success).length,
    total: categoryResults.length
  };
}

async function generateReport(testResults: TestSuiteResult, idempotencyTested: boolean = false): Promise<void> {
  console.log('\n' + '='.repeat(80));
  console.log('📊 COMPREHENSIVE WEBHOOK TEST REPORT');
  if (idempotencyTested) {
    console.log('🔄 WITH IDEMPOTENCY VALIDATION');
  }
  console.log('='.repeat(80));
  
  // Overall Results
  console.log(`📈 Overall Results:`);
  console.log(`   Total Tests: ${testResults.totalTests}`);
  console.log(`   Passed: ${testResults.passedTests}`);
  console.log(`   Failed: ${testResults.failedTests}`);
  console.log(`   Success Rate: ${((testResults.passedTests / testResults.totalTests) * 100).toFixed(1)}%`);
  console.log(`   Duration: ${testResults.duration.toFixed(2)}s`);
  
  // Category Breakdown
  console.log(`\n📂 Results by Category:`);
  Object.entries(testResults.categories).forEach(([category, stats]) => {
    if (stats.total > 0) {
      const rate = ((stats.passed / stats.total) * 100).toFixed(1);
      const status = stats.passed === stats.total ? '✅' : '⚠️';
      console.log(`   ${status} ${category.toUpperCase()}: ${stats.passed}/${stats.total} (${rate}%)`);
    }
  });
  
  // Failed Tests Details
  const failedTests = testResults.results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log(`\n❌ Failed Tests (${failedTests.length}):`);
    failedTests.forEach(result => {
      console.log(`   • ${result.scenario.name}`);
      console.log(`     Reason: ${result.message}`);
      if (result.details.webhookResult && !result.details.webhookResult.success) {
        console.log(`     Webhook: ${result.details.webhookResult.message}`);
      }
    });
  }
  
  // Webhook Event Coverage
  console.log(`\n🎯 Webhook Event Coverage:`);
  const testedEvents = new Set(testResults.results.map(r => r.scenario.webhookEvent));
  const allEvents = [
    'subscription.authenticated',
    'subscription.activated', 
    'subscription.charged',
    'subscription.pending',
    'subscription.halted',
    'subscription.cancelled',
    'subscription.completed',
    'subscription.expired',
    'subscription.updated'
  ];
  
  allEvents.forEach(event => {
    const tested = testedEvents.has(event);
    const status = tested ? '✅' : '❌';
    console.log(`   ${status} ${event}`);
  });
  
  // State Transition Coverage
  console.log(`\n🔄 State Transition Coverage:`);
  const transitions = new Set();
  testResults.results.forEach(r => {
    const initial = r.scenario.initialState.subscription_status;
    const final = r.scenario.expectedFinalState.subscription_status;
    transitions.add(`${initial} → ${final}`);
  });

  Array.from(transitions).sort().forEach(transition => {
    console.log(`   ✅ ${transition}`);
  });

  // Payment Method Coverage
  console.log(`\n💳 Payment Method Coverage:`);
  const paymentMethods = new Set();
  const createNewCancelOldScenarios: SubscriptionScenario[] = [];
  testResults.results.forEach(r => {
    if (r.scenario.paymentMethod) {
      paymentMethods.add(r.scenario.paymentMethod);
    }
    if (r.scenario.isCreateNewCancelOld) {
      createNewCancelOldScenarios.push(r.scenario);
    }
  });

  ['card', 'upi', 'emandate'].forEach(method => {
    const tested = paymentMethods.has(method);
    const status = tested ? '✅' : '❌';
    console.log(`   ${status} ${method.toUpperCase()} payment method`);
  });

  // Create-New-Cancel-Old Pattern Coverage
  console.log(`\n🔄 Create-New-Cancel-Old Pattern Coverage:`);
  if (createNewCancelOldScenarios.length > 0) {
    console.log(`   ✅ UPI/E-Mandate subscription replacement tested (${createNewCancelOldScenarios.length} scenarios)`);
    createNewCancelOldScenarios.forEach(scenario => {
      console.log(`   ✅ ${scenario.name}`);
    });
  } else {
    console.log(`   ❌ No create-new-cancel-old scenarios tested`);
  }

  // Idempotency Validation
  if (idempotencyTested) {
    console.log(`\n🔄 Idempotency Validation:`);
    const idempotencyResults = testResults.results.filter(r => r.details.idempotencyResult);
    const idempotencyPassed = idempotencyResults.filter(r => r.details.idempotencyResult?.success).length;
    const idempotencyTotal = idempotencyResults.length;

    if (idempotencyTotal > 0) {
      const idempotencyRate = ((idempotencyPassed / idempotencyTotal) * 100).toFixed(1);
      console.log(`   ✅ Duplicate webhook handling: ${idempotencyPassed}/${idempotencyTotal} (${idempotencyRate}%)`);
      console.log(`   ✅ processed_webhook_events table usage validated`);
      console.log(`   ✅ x-razorpay-event-id header validation working`);
      console.log(`   ✅ State consistency during duplicates maintained`);
    } else {
      console.log(`   ⚠️  No idempotency tests were run`);
    }
  }

  console.log('\n' + '='.repeat(80));

  if (testResults.passedTests === testResults.totalTests) {
    console.log('🎉 ALL WEBHOOK TESTS PASSED!');
    console.log('🛡️  Subscription lifecycle fully tested!');
    console.log('✅ All webhook events working correctly');
    console.log('✅ All state transitions validated');
    console.log('✅ Database operations atomic and consistent');
    if (idempotencyTested) {
      console.log('✅ Webhook idempotency fully validated');
      console.log('✅ Duplicate webhook protection working');
    }
  } else {
    console.log('⚠️  Some tests failed. Review the details above.');
    console.log('🔧 Fix failing scenarios before production deployment.');
  }

  console.log('='.repeat(80));
}

async function main() {
  try {
    // Check for idempotency testing flag
    const testIdempotency = process.argv.includes('--idempotency') || process.argv.includes('-i');

    const testResults = await runComprehensiveTests(testIdempotency);
    await generateReport(testResults, testIdempotency);

    // Exit with appropriate code
    process.exit(testResults.passedTests === testResults.totalTests ? 0 : 1);

  } catch (error) {
    console.error('💥 Test suite failed with error:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { runComprehensiveTests, generateReport };
